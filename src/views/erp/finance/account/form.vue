<template>
	<div class="system-account-dialog-container">
		<el-dialog :close-on-click-modal="false" :title="dataForm.id ? $t('common.editBtn') : $t('common.addBtn')" draggable v-model="visible">
			<el-form :model="dataForm" :rules="dataRules" label-width="120px" ref="dataFormRef" v-loading="loading">
				<el-row :gutter="20">
					<el-col :span="12" class="mb20">
						<el-form-item :label="$t('account.name')" prop="name">
							<el-input :placeholder="$t('account.inputNameTip')" v-model="dataForm.name"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item :label="$t('account.no')" prop="no">
							<el-input :placeholder="$t('account.inputNoTip')" v-model="dataForm.no"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item :label="$t('account.status')" prop="status">
							<el-radio-group v-model="dataForm.status">
								<el-radio :key="item.value" :label="item.value" v-for="item in enable_and_disable_status">{{ item.label }}</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item :label="$t('account.sort')" prop="sort">
							<el-input-number :placeholder="$t('account.inputSortTip')" v-model="dataForm.sort" :min="0" :max="999" controls-position="right" class="w100"></el-input-number>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="是否默认" prop="isDefault">
							<el-radio-group v-model="dataForm.isDefault">
								<el-radio label="1">是</el-radio>
								<el-radio label="0">否</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :span="24" class="mb20">
						<el-form-item :label="$t('account.remark')" prop="remark">
							<el-input :placeholder="$t('account.inputRemarkTip')" type="textarea" v-model="dataForm.remark" :rows="3"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
					<el-button type="primary" @click="onSubmit" :loading="loading">{{ $t('common.confirmButtonText') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" name="accountDialog" setup>
import { addObj, getObj, putObj } from '/@/api/erp/account';
import { useDict } from '/@/hooks/dict';
import { useI18n } from 'vue-i18n';
import { useMessage } from '/@/hooks/message';

const { t } = useI18n();

// 定义刷新表格emit
const emit = defineEmits(['refresh']);
// @ts-ignore
const { enable_and_disable_status } = useDict('enable_and_disable_status');

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

const dataForm = reactive({
	id: null,
	name: '',
	no: '',
	remark: '',
	status: '1',
	sort: 0,
	isDefault: '0',
});

const dataRules = ref({
	name: [
		{
			required: true,
			message: t('account.name') + t('common.cannotBeEmpty'),
			trigger: 'blur',
		},
	],
	no: [
		{
			required: true,
			message: t('account.no') + t('common.cannotBeEmpty'),
			trigger: 'blur',
		},
	],
	status: [
		{
			required: true,
			message: t('account.status') + t('common.cannotBeEmpty'),
			trigger: 'blur',
		},
	],
	sort: [
		{
			required: true,
			message: t('account.sort') + t('common.cannotBeEmpty'),
			trigger: 'blur',
		},
	],
});

// 打开弹窗
const openDialog = (id?: string) => {
	visible.value = true;
	dataForm.id = null;

	// 重置表单数据
	if (dataFormRef.value) {
		dataFormRef.value.resetFields();
	}

	// 重置数据
	dataForm.name = '';
	dataForm.no = '';
	dataForm.remark = '';
	dataForm.status = '1';
	dataForm.sort = 0;
	dataForm.isDefault = '0';

	// 获取数据信息
	if (id) {
		dataForm.id = id;
		getAccountData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		const response = dataForm.id ? await putObj(dataForm) : await addObj(dataForm);
		if (response && response.code === 0) {
			useMessage().success(dataForm.id ? t('common.editSuccessText') : t('common.addSuccessText'));
			visible.value = false;
			emit('refresh');
		} else if (response && response.code !== 0) {
			useMessage().error(response.msg);
		}
	} catch (err) {
		useMessage().error(err as string);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getAccountData = (id: string) => {
	// 获取数据
	getObj(id).then((res: any) => {
		Object.assign(dataForm, res.data);
	});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
