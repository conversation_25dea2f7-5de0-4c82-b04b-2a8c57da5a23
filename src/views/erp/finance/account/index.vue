<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
					<el-form-item :label="$t('account.name')" prop="name">
						<el-input v-model="state.queryForm.name" :placeholder="$t('account.inputNameTip')" clearable />
					</el-form-item>
					<el-form-item :label="$t('account.no')" prop="no">
						<el-input v-model="state.queryForm.no" :placeholder="$t('account.inputNoTip')" clearable />
					</el-form-item>
					<el-form-item :label="$t('account.status')" prop="status">
						<el-select :placeholder="$t('account.selectStatusTip')" class="w100" clearable v-model="state.queryForm.status">
							<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in enable_and_disable_status" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
						<el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button v-auth="'erp_account_add'" icon="folder-add" type="primary" @click="accountDialogRef.openDialog()">
						{{ $t('common.addBtn') }}
					</el-button>
					<el-button
						v-auth="'erp_account_del'"
						:disabled="multiple"
						icon="delete"
						type="danger"
						@click="handleDelete(selectObjs)"
					>
						{{ $t('common.delBtn') }}
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'erp_account_export'"
						@exportExcel="exportExcel"
						class="ml10"
						style="float: right; margin-right: 20px"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="50" />
				<el-table-column type="index" :label="$t('account.index')" width="60" />
				<el-table-column :label="$t('account.name')" prop="name" show-overflow-tooltip />
				<el-table-column :label="$t('account.no')" prop="no" show-overflow-tooltip />
				<el-table-column :label="$t('account.status')" align="center" prop="status" width="80">
					<template #default="scope">
						<dict-tag :options="enable_and_disable_status" :value="scope.row.status" />
					</template>
				</el-table-column>
				<el-table-column :label="$t('account.isDefault')" align="center" prop="isDefault" width="100">
					<template #default="scope">
						<el-tag :type="scope.row.isDefault === '1' ? 'success' : 'info'">
							{{ scope.row.isDefault === '1' ? '是' : '否' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column :label="$t('account.createTime')" prop="createTime" show-overflow-tooltip width="160" />
				<el-table-column :label="$t('account.createBy')" prop="createBy" show-overflow-tooltip width="120" />
				<el-table-column :label="$t('common.action')" width="180" fixed="right">
					<template #default="scope">
						<el-button v-auth="'erp_account_edit'" icon="edit-pen" text type="primary" @click="accountDialogRef.openDialog(scope.row.id)">
							{{ $t('common.editBtn') }}
						</el-button>
						<el-button v-auth="'erp_account_del'" icon="delete" text type="primary" @click="handleDelete([scope.row.id])">
							{{ $t('common.delBtn') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"></pagination>
		</div>
		<account-form ref="accountDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script lang="ts" name="erpAccount" setup>
import { delObj, pageList } from '/@/api/erp/account';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 动态引入组件
const AccountForm = defineAsyncComponent(() => import('./form.vue'));

const { t } = useI18n();

const { enable_and_disable_status } = useDict('enable_and_disable_status');

// 定义变量内容
const accountDialogRef = ref();
const queryRef = ref();
const showSearch = ref(true);

// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		name: '',
		no: '',
		status: '',
	},
	pageList: pageList,
});

const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	getDataList();
};

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map((val: any) => val.id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除选中数据，是否继续?');
		const response = await delObj(ids);
		if (response && response.code === 0) {
			useMessage().success(t('common.delSuccessText'));
			getDataList();
		} else if (response && response.code !== 0) {
			useMessage().error(response.msg);
		}
	} catch {}
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/erp/account/export', state.queryForm, 'account.xlsx');
};
</script>
