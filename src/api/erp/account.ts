import request from '/@/utils/request';

export const pageList = (params?: Object) => {
	return request({
		url: '/admin/erp/account/page',
		method: 'get',
		params,
	});
};

export const getList = (params?: Object) => {
	return request({
		url: '/admin/erp/account/getList',
		method: 'get',
		params,
	});
};

export const addObj = (obj: Object) => {
	return request({
		url: '/admin/erp/account',
		method: 'post',
		data: obj,
	});
};

export const getObj = (id: String) => {
	return request({
		url: '/admin/erp/account/' + id,
		method: 'get',
	});
};

export const delObj = (ids: Object) => {
	return request({
		url: '/admin/erp/account',
		method: 'delete',
		data: ids,
	});
};

export const putObj = (obj: Object) => {
	return request({
		url: '/admin/erp/account',
		method: 'put',
		data: obj,
	});
};
